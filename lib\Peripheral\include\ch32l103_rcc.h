/********************************** (C) COPYRIGHT  *******************************
 * File Name          : ch32l103_rcc.h
 * Author             : WCH
 * Version            : V1.0.0
 * Date               : 2023/07/08
 * Description        : This file provides all the RCC firmware functions.
 *********************************************************************************
 * Copyright (c) 2021 Nanjing Qinheng Microelectronics Co., Ltd.
 * Attention: This software (modified or not) and binary are used for
 * microcontroller manufactured by Nanjing Qinheng Microelectronics.
 *******************************************************************************/
#ifndef __CH32L103_RCC_H
#define __CH32L103_RCC_H

#ifdef __cplusplus
extern "C" {
#endif

#include "ch32l103.h"

/* RCC_Exported_Types */
typedef struct
{
    uint32_t SYSCLK_Frequency; /* returns SYSCLK clock frequency expressed in Hz */
    uint32_t HCLK_Frequency;   /* returns HCLK clock frequency expressed in Hz */
    uint32_t PCLK1_Frequency;  /* returns PCLK1 clock frequency expressed in Hz */
    uint32_t PCLK2_Frequency;  /* returns PCLK2 clock frequency expressed in Hz */
    uint32_t ADCCLK_Frequency; /* returns ADCCLK clock frequency expressed in Hz */
} RCC_ClocksTypeDef;

/* HSE_configuration */
#define RCC_HSE_OFF                     ((uint32_t)0x00000000)
#define RCC_HSE_ON                      ((uint32_t)0x00010000)
#define RCC_HSE_Bypass                  ((uint32_t)0x00040000)

/* PLL_entry_clock_source */
#define RCC_PLLSource_HSI_Div2          ((uint32_t)0x00000000)
#define RCC_PLLSource_HSE_Div1          ((uint32_t)0x00010000)
#define RCC_PLLSource_HSE_Div2          ((uint32_t)0x00030000)

/* PLL_multiplication_factor for other CH32L103  */
#define RCC_PLLMul_2                    ((uint32_t)0x00000000)
#define RCC_PLLMul_3                    ((uint32_t)0x00040000)
#define RCC_PLLMul_4                    ((uint32_t)0x00080000)
#define RCC_PLLMul_5                    ((uint32_t)0x000C0000)
#define RCC_PLLMul_6                    ((uint32_t)0x00100000)
#define RCC_PLLMul_7                    ((uint32_t)0x00140000)
#define RCC_PLLMul_8                    ((uint32_t)0x00180000)
#define RCC_PLLMul_9                    ((uint32_t)0x001C0000)
#define RCC_PLLMul_10                   ((uint32_t)0x00200000)
#define RCC_PLLMul_11                   ((uint32_t)0x00240000)
#define RCC_PLLMul_12                   ((uint32_t)0x00280000)
#define RCC_PLLMul_13                   ((uint32_t)0x002C0000)
#define RCC_PLLMul_14                   ((uint32_t)0x00300000)
#define RCC_PLLMul_15                   ((uint32_t)0x00340000)
#define RCC_PLLMul_16                   ((uint32_t)0x00380000)
#define RCC_PLLMul_18                   ((uint32_t)0x003C0000)

/* System_clock_source */
#define RCC_SYSCLKSource_HSI            ((uint32_t)0x00000000)
#define RCC_SYSCLKSource_HSE            ((uint32_t)0x00000001)
#define RCC_SYSCLKSource_PLLCLK         ((uint32_t)0x00000002)

/* HB_clock_source */
#define RCC_SYSCLK_Div1                 ((uint32_t)0x00000000)
#define RCC_SYSCLK_Div2                 ((uint32_t)0x00000080)
#define RCC_SYSCLK_Div4                 ((uint32_t)0x00000090)
#define RCC_SYSCLK_Div8                 ((uint32_t)0x000000A0)
#define RCC_SYSCLK_Div16                ((uint32_t)0x000000B0)
#define RCC_SYSCLK_Div64                ((uint32_t)0x000000C0)
#define RCC_SYSCLK_Div128               ((uint32_t)0x000000D0)
#define RCC_SYSCLK_Div256               ((uint32_t)0x000000E0)
#define RCC_SYSCLK_Div512               ((uint32_t)0x000000F0)

/* PB1_PB2_clock_source */
#define RCC_HCLK_Div1                   ((uint32_t)0x00000000)
#define RCC_HCLK_Div2                   ((uint32_t)0x00000400)
#define RCC_HCLK_Div4                   ((uint32_t)0x00000500)
#define RCC_HCLK_Div8                   ((uint32_t)0x00000600)
#define RCC_HCLK_Div16                  ((uint32_t)0x00000700)

/* RCC_Interrupt_source */
#define RCC_IT_LSIRDY                   ((uint8_t)0x01)
#define RCC_IT_LSERDY                   ((uint8_t)0x02)
#define RCC_IT_HSIRDY                   ((uint8_t)0x04)
#define RCC_IT_HSERDY                   ((uint8_t)0x08)
#define RCC_IT_PLLRDY                   ((uint8_t)0x10)
#define RCC_IT_CSS                      ((uint8_t)0x80)

/* USB_Device_clock_source */
#define RCC_USBCLKSource_PLLCLK_Div1    ((uint32_t)0x00000000)
#define RCC_USBCLKSource_PLLCLK_Div2    ((uint32_t)0x00400000)
#define RCC_USBCLKSource_PLLCLK_Div1_5  ((uint32_t)0x00800000)

/* ADC_clock_source */
#define RCC_PCLK2_Div2                 ((uint32_t)0x00000000)
#define RCC_PCLK2_Div4                 ((uint32_t)0x00004000)
#define RCC_PCLK2_Div6                 ((uint32_t)0x00008000)
#define RCC_PCLK2_Div8                 ((uint32_t)0x0000C000)
#define RCC_HCLK_ADC                   ((uint32_t)0x80000000)

/* LSE_configuration */
#define RCC_LSE_OFF                    ((uint8_t)0x00)
#define RCC_LSE_ON                     ((uint8_t)0x01)
#define RCC_LSE_Bypass                 ((uint8_t)0x04)

/* RTC_clock_source */
#define RCC_RTCCLKSource_LSE           ((uint32_t)0x00000100)
#define RCC_RTCCLKSource_LSI           ((uint32_t)0x00000200)
#define RCC_RTCCLKSource_HSE_Div128    ((uint32_t)0x00000300)

/* HB_peripheral */
#define RCC_HBPeriph_DMA1             ((uint32_t)0x00000001)
#define RCC_HBPeriph_SRAM             ((uint32_t)0x00000004)
#define RCC_HBPeriph_CRC              ((uint32_t)0x00000040)
#define RCC_HBPeriph_USBFS            ((uint32_t)0x00001000)
#define RCC_HBPeriph_USBPD            ((uint32_t)0x00020000)

/* PB2_peripheral */
#define RCC_PB2Periph_AFIO            ((uint32_t)0x00000001)
#define RCC_PB2Periph_GPIOA           ((uint32_t)0x00000004)
#define RCC_PB2Periph_GPIOB           ((uint32_t)0x00000008)
#define RCC_PB2Periph_GPIOC           ((uint32_t)0x00000010)
#define RCC_PB2Periph_GPIOD           ((uint32_t)0x00000020)
#define RCC_PB2Periph_ADC1            ((uint32_t)0x00000200)
#define RCC_PB2Periph_TIM1            ((uint32_t)0x00000800)
#define RCC_PB2Periph_SPI1            ((uint32_t)0x00001000)
#define RCC_PB2Periph_USART1          ((uint32_t)0x00004000)

/* PB1_peripheral */
#define RCC_PB1Periph_TIM2            ((uint32_t)0x00000001)
#define RCC_PB1Periph_TIM3            ((uint32_t)0x00000002)
#define RCC_PB1Periph_TIM4            ((uint32_t)0x00000004)
#define RCC_PB1Periph_WWDG            ((uint32_t)0x00000800)
#define RCC_PB1Periph_SPI2            ((uint32_t)0x00004000)
#define RCC_PB1Periph_USART2          ((uint32_t)0x00020000)
#define RCC_PB1Periph_USART3          ((uint32_t)0x00040000)
#define RCC_PB1Periph_USART4          ((uint32_t)0x00080000)
#define RCC_PB1Periph_I2C1            ((uint32_t)0x00200000)
#define RCC_PB1Periph_I2C2            ((uint32_t)0x00400000)
#define RCC_PB1Periph_CAN1            ((uint32_t)0x02000000)
#define RCC_PB1Periph_BKP             ((uint32_t)0x08000000)
#define RCC_PB1Periph_PWR             ((uint32_t)0x10000000)
#define RCC_PB1Periph_LPTIM           ((uint32_t)0x80000000)

/* Clock_source_to_output_on_MCO_pin */
#define RCC_MCO_NoClock                ((uint8_t)0x00)
#define RCC_MCO_SYSCLK                 ((uint8_t)0x04)
#define RCC_MCO_HSI                    ((uint8_t)0x05)
#define RCC_MCO_HSE                    ((uint8_t)0x06)
#define RCC_MCO_PLLCLK_Div2            ((uint8_t)0x07)

/* RCC_Flag */
#define RCC_FLAG_HSIRDY                ((uint8_t)0x21)
#define RCC_FLAG_HSERDY                ((uint8_t)0x31)
#define RCC_FLAG_PLLRDY                ((uint8_t)0x39)
#define RCC_FLAG_LSERDY                ((uint8_t)0x41)
#define RCC_FLAG_LSIRDY                ((uint8_t)0x61)
#define RCC_FLAG_PINRST                ((uint8_t)0x7A)
#define RCC_FLAG_PORRST                ((uint8_t)0x7B)
#define RCC_FLAG_SFTRST                ((uint8_t)0x7C)
#define RCC_FLAG_IWDGRST               ((uint8_t)0x7D)
#define RCC_FLAG_WWDGRST               ((uint8_t)0x7E)
#define RCC_FLAG_LPWRRST               ((uint8_t)0x7F)

/* SysTick_clock_source */
#define SysTick_CLKSource_HCLK_Div8    ((uint32_t)0xFFFFFFFB)
#define SysTick_CLKSource_HCLK         ((uint32_t)0x00000004)

/* ADC_clock_H_Level_Duty_Cycle */
#define RCC_ADC_H_Level_Mode0   ((uint32_t)0x00000000)
#define RCC_ADC_H_Level_Mode1   ((uint32_t)0x10000000)
#define RCC_ADC_H_Level_Mode2   ((uint32_t)0x20000000)
#define RCC_ADC_H_Level_Mode3   ((uint32_t)0x30000000)
#define RCC_ADC_H_Level_Mode4   ((uint32_t)0x40000000)
#define RCC_ADC_H_Level_Mode5   ((uint32_t)0x50000000)
#define RCC_ADC_H_Level_Mode6   ((uint32_t)0x60000000)
#define RCC_ADC_H_Level_Mode7   ((uint32_t)0x70000000)

void        RCC_DeInit(void);
void        RCC_HSEConfig(uint32_t RCC_HSE);
ErrorStatus RCC_WaitForHSEStartUp(void);
void        RCC_AdjustHSICalibrationValue(uint8_t HSICalibrationValue);
void        RCC_HSICmd(FunctionalState NewState);
void        RCC_PLLConfig(uint32_t RCC_PLLSource, uint32_t RCC_PLLMul);
void        RCC_PLLCmd(FunctionalState NewState);
void        RCC_SYSCLKConfig(uint32_t RCC_SYSCLKSource);
uint8_t     RCC_GetSYSCLKSource(void);
void        RCC_HCLKConfig(uint32_t RCC_SYSCLK);
void        RCC_PCLK1Config(uint32_t RCC_HCLK);
void        RCC_PCLK2Config(uint32_t RCC_HCLK);
void        RCC_ITConfig(uint8_t RCC_IT, FunctionalState NewState);
void        RCC_USBCLKConfig(uint32_t RCC_USBCLKSource);
void        RCC_ADCCLKConfig(uint32_t RCC_PCLK2);
void        RCC_LSEConfig(uint8_t RCC_LSE);
void        RCC_LSICmd(FunctionalState NewState);
void        RCC_RTCCLKConfig(uint32_t RCC_RTCCLKSource);
void        RCC_RTCCLKCmd(FunctionalState NewState);
void        RCC_GetClocksFreq(RCC_ClocksTypeDef *RCC_Clocks);
void        RCC_HBPeriphClockCmd(uint32_t RCC_HBPeriph, FunctionalState NewState);
void        RCC_PB2PeriphClockCmd(uint32_t RCC_PB2Periph, FunctionalState NewState);
void        RCC_PB1PeriphClockCmd(uint32_t RCC_PB1Periph, FunctionalState NewState);
void        RCC_PB2PeriphResetCmd(uint32_t RCC_PB2Periph, FunctionalState NewState);
void        RCC_PB1PeriphResetCmd(uint32_t RCC_PB1Periph, FunctionalState NewState);
void        RCC_BackupResetCmd(FunctionalState NewState);
void        RCC_ClockSecuritySystemCmd(FunctionalState NewState);
void        RCC_MCOConfig(uint8_t RCC_MCO);
FlagStatus  RCC_GetFlagStatus(uint8_t RCC_FLAG);
void        RCC_ClearFlag(void);
ITStatus    RCC_GetITStatus(uint8_t RCC_IT);
void        RCC_ClearITPendingBit(uint8_t RCC_IT);
void        RCC_ADCCLKDutyCycleConfig(uint32_t RCC_DutyCycle);
void        RCC_HSE_LP_Cmd(FunctionalState NewState);
void        RCC_HSI_LP_Cmd(FunctionalState NewState);

#ifdef __cplusplus
}
#endif

#endif
