/* Includes -----------------------------------------------------------------*/
#include "M0_Control_Library.h"
/* Private typedef ----------------------------------------------------------*/
/* Private variables --------------------------------------------------------*/
/* Variables ----------------------------------------------------------------*/
static volatile uint16_t Timewait = 0;//�ӳٻ�ȴ�ʹ�õ�ʱ�䣬ʱ���׼Ϊ�����������ж�

Speedrampctr_Type   SpeedRamp_M;        //�Ӽ��ٿ��ƽṹ��
Powercontrol_Type   Powerctr_M;         //���µ���ƽṹ��
Operation_Type      Operation_M;        //ϵͳ�����ṹ��
/******************************************************************************
* Function Name  : Motor_Operation_Process
* Description    : �����������
* Input          : None
* Output         : None
* Return         : None
******************************************************************************/
void Motor_Operation_Process(void)
{
    //�������״̬������
    switch (RunningStatus_M)
    {
        case IDLE://�������������
            if((Powerctr_M.Poweron == TRUE)&&(Key_M.ACkey_Poweron == TRUE))
            {
                Key_Memread(&Key_M,&Flash_M);
                if((LED_M.PSC.Mark == FALSE)&&(LED_M.PSC.Process ==TRUE))//�Լ���Ч�����Լ��ж���ִ������
                {
                    if(Key_M.Skey_rank >0)
                    {
                        if(RunningStatus_M != FAULT)            //�������״̬����Ϊ����
                       RunningStatus_M = INIT;
                    }
                }
            }

            break;
        case INIT:
            Motor_Start_Init_M();       //���������ʼ��
            Time_Delay_Set(SpeedRamp_M.Precharge_cntset,&SpeedRamp_M.Precharge_cnt);
            RunningStatus_M = PRESTART;    //�������״̬�л���Ԥ������
            Operation_M.Runstatuemark=TRUE;
            break;

        case PRESTART://�ȴ���ʱ�������ж���ʽ����
            break;

        case POSITION://��λ���ڶ�λ�����д����л�������״̬
            break;

        case START://�����������������д����л�������״̬
            break;

        case RUN://����
            break;

        case BRAKE://ɲ��
            Led_OFF(&LED_M);
             Key_Memwrite(&Key_M,&Flash_M,Powerctr_M.DCVoltAver);
            break;

        case STOP://ֹͣ
            TIM_CtrlPWMOutputs(TIM1, DISABLE);  //PWM�����ֹ
            TRIAC_OFF();
            RunningStatus_M = WAIT;             //�������״̬��ת����WAIT״̬
            //ADC_1R_Start(DISABLE);                 //ADCת����ֹ
            Led_OFF(&LED_M);
            Time_Delay_Set(Operation_M.StopwaitSet,&Operation_M.StopwaitCnt);//ͣ���ȴ��ӳ��趨
		   break;

        case WAIT://�ȴ�
            //ͣ���ȴ�ʱ�䵽����λIDLE״̬
            if (Time_Delay_Elapse(Operation_M.StopwaitCnt) == TRUE)
            {
                RunningStatus_M=IDLE;
            }
            break;
        case FAULT://����
            if((Operation_M.FaultMem==FALSE)&&(Flash_M.ReadMark==TRUE))//������Ͻ���һ��FLASH����
            {
                Key_Memwrite(&Key_M,&Flash_M,Powerctr_M.DCVoltAver);
                Operation_M.FaultMem=TRUE;
            }
             LED_Flicker(&LED_M);
             TRIAC_OFF();
            break;

        default:
            break;
    }
}
/******************************************************************************
* Function Name  : Speedrampctr_Parameter_Define
* Description    : ת�ٿ��Ʋ�������
* Input          : �Ӽ��ٿ��ƽṹ��ָ��
* Output         : None
* Return         : None
*****************************************************************************/
void Speedrampctr_Parameter_Define(Speedrampctr_Type *Speed_Ramp)
{
    Speed_Ramp->Direction=STARTUP_DIRECTION;

    Speed_Ramp->Aftramp = RAMP_INJ_SPEED_M*Speed_Ramp->Direction;

    Speed_Ramp->AccStep = _IQ(1.0)/(SPEED_LOOP_CAL_FREQ*ACCE_TIME_M);
    Speed_Ramp->DecStep = _IQ(1.0)/(SPEED_LOOP_CAL_FREQ*DECE_TIME_M);
    Speed_Ramp->PoweroffDecStep= _IQ(1.0)/(SPEED_LOOP_CAL_FREQ*POWEROFF_DECE_TIME_M);

    Speed_Ramp->Startfinishspeed = START_FINISH_SPEED;
    Speed_Ramp->Startfinish =FALSE;
    Speed_Ramp->Faststopspeed = FAST_DECE_SPEED*Speed_Ramp->Direction;

    Speed_Ramp->Break =  FALSE;
    Speed_Ramp->BreakStop = FALSE;
    Speed_Ramp->Breakspeed = BREAKSPEED;
    Speed_Ramp->Breakwait_cntset = (uint16_t)(BREAKTIME*SPEED_RAMP_FREQ_M);

    Speed_Ramp->Precharge_cntset = (uint16_t)(PRECHAGE_TIME*SPEED_RAMP_FREQ_M);

    //ĸ�ߵ�ѹ�������˲�����ϵ��
    Speed_Ramp->VdcLPF1st.Input_Coef = _IQdiv(VDCLPF_SPEEDLIM_WcT_M,(VDCLPF_SPEEDLIM_WcT_M + UNIT_Q24));
    Speed_Ramp->VdcLPF1st.Output_Coef = _IQdiv(UNIT_Q24,(VDCLPF_SPEEDLIM_WcT_M + UNIT_Q24));
    Speed_Ramp->VdcLPF1st.Out_New =_IQ(1.0);
    Speed_Ramp->VdcLPF1st.Out_Pre =_IQ(1.0);
    Speed_Ramp->Voltbegin = VDC_SPEEDLIM_BEGIN;
    Speed_Ramp->Voltrelease = VDC_SPEEDLIM_RELEASE;
    Speed_Ramp->Speedlimmark = FALSE;
    Speed_Ramp->Vdclimcoef = _IQdiv(_IQ(1.0),_IQmpy(VDC_SPEEDLIM_ADJ,VDC_SPEEDLIM_BEGIN));
}
/******************************************************************************
* Function Name  : Speedrampctr_Start_Init_M
* Description    : ת�ٿ��Ʋ�����ʼ��
* Input          : �Ӽ��ٿ��ƽṹ��ָ��
* Output         : None
* Return         : None
*****************************************************************************/
void Speedrampctr_Start_Init_M(Speedrampctr_Type *Speed_Ramp)
{
    Speed_Ramp->Aftramp = RAMP_INJ_SPEED_M*Speed_Ramp->Direction;
    Speed_Ramp->Startfinish =FALSE;
    Speed_Ramp->BreakStop = FALSE;
    Speed_Ramp->Faststopspeed = FAST_DECE_SPEED*Speed_Ramp->Direction;
    Speed_Ramp->Break =  FALSE;
    Speed_Ramp->Speedlimmark = FALSE;

    Speed_Ramp->VdcLPF1st.Out_New =ADC_M.DCVoltAver;
    Speed_Ramp->VdcLPF1st.Out_Pre =ADC_M.DCVoltAver;
}
/******************************************************************************
* Function Name  : Poweroperation_Initial
* Description    : ���µ����ʼ����״̬��ʼ��
* Input          : ���µ���ƽṹ��ָ��
* Output         : None
* Return         : None
*****************************************************************************/
void Poweroperation_Initial(Powercontrol_Type *PowerOper)
{
    PowerOper->Poweron_CntThresh = (uint16_t)(POWERVERY_FREQ*POWERON_TIME);
    PowerOper->Poweron_Volt = POWERON_VOLT;
    PowerOper->Poweroff_Volt1 = POWEROFF_VOLT1;
    PowerOper->Poweroff_Volt2 = POWEROFF_VOLT2;
    PowerOper->Poweron = FALSE;
    PowerOper->PoweroffBrake=FALSE;
    PowerOper->Poweron_Cnt = 0;
    //ĸ�ߵ�ѹ�˲�����ϵ��
    PowerOper->DCVoltAverSizeInv1 = _IQ(1.0)/POWERVERY_DCVOLTAVERSIZE;
    PowerOper->DCVoltAverSizeInv2 = _IQ(1.0) - PowerOper->DCVoltAverSizeInv1;
    PowerOper->DCVoltAver = 0;
}
/******************************************************************************
* Function Name  : Time_Delay_Process
* Description    : �����������ж�ʱ��(0.5ms)Ϊ��׼�ı����ݼ�����
* Input          : None
* Output         : None
* Return         : None
******************************************************************************/
void Time_Delay_Process(void)
{
    if (Timewait != 0)
        Timewait --;

    if (Operation_M.StopwaitCnt != 0)
        Operation_M.StopwaitCnt --;

    if (Operation_M.FaultHoldingCnt != 0)
        Operation_M.FaultHoldingCnt--;

    if (SpeedRamp_M.Breakwait_cnt != 0)
        SpeedRamp_M.Breakwait_cnt --;

    if (SpeedRamp_M.Precharge_cnt != 0)
        SpeedRamp_M.Precharge_cnt --;

    if(Operation_M.FaultwaitCnt!=0)
        Operation_M.FaultwaitCnt--;

    if (Triac_ctl.Zero.ZcrossDelayTrigcnt != 0)
         Triac_ctl.Zero.ZcrossDelayTrigcnt--;

    if (Triac_ctl.TonEnable_cnt != 0)
         Triac_ctl.TonEnable_cnt--;

    if (LED_M.FlickCnt != 0)
        LED_M.FlickCnt --;

    if (LED_M.PSC.Ledcnt != 0)
        LED_M.PSC.Ledcnt --;

}
/******************************************************************************
* Function Name  : Time_Wait
* Description    : The function wait for a delay to be over.
* Input          : None
* Output         : None
* Return         : None
******************************************************************************/
void Time_Wait(uint16_t time)
{
    Timewait = time;
    while (Timewait != 0)
    {;}
}
/******************************************************************************
* Function Name  : Time_Delay_Set
* Description    : ��ʱʱ�����ú���
* Input          : ��ʱʱ��, ��ʱ��������ָ��
* Output         : None
* Return         : None
******************************************************************************/
void Time_Delay_Set(uint16_t Delayset, uint16_t *Delaycounter)
{
    *Delaycounter = Delayset;
}
/******************************************************************************
* Function Name  : Time_Delay_Elapse
* Description    : ��ʱʱ�䵽�ж�
* Input          : ��ʱ��������
* Output         : �ж������ʱ�䵽��TRUE,ʱ��δ����FALSE
* Return         : �ж����
******************************************************************************/
Truth_Verify_Type Time_Delay_Elapse(uint16_t Delaycounter)
{
     if (Delaycounter == 0)
       return (TRUE);
     else
       return (FALSE);
}
/******************************************************************************
* Function Name  : Operation_Parameter_Define
* Description    : ������������
* Input          : �������ƽṹ��ָ��
* Output         : None
* Return         : None
*****************************************************************************/
void Operation_Parameter_Define(Operation_Type *Operastruc)
{
    Operastruc->StopwaitSet = (uint16_t)(STOP_WAITING_TIME * OPERATION_CAL_FREQ);
    Operastruc->FaultHoldingSet = (uint16_t)(FAULT_HOLDING_TIME * OPERATION_CAL_FREQ);
    Operastruc->FaultwaitSet = (uint16_t)(FAULT_WAIT_TIME * OPERATION_CAL_FREQ);
    Operastruc->FaultClean = FALSE;
    Operastruc->Faultstop = FALSE;
    Operastruc->FaultMem = FALSE;

    Operastruc->Runstatuemark = FALSE;
    Operastruc->PoweroffMemmark = FALSE;
}
/******************************************************************************
* Function Name  : Operation_Status_Init
* Description    : ����״̬��ʼ��
* Input          : �������ƽṹ��ָ��
* Output         : None
* Return         : None
*****************************************************************************/
void Operation_Status_Init(Operation_Type *Operastruc)
{
    Operastruc->StopwaitCnt = 0;
    Operastruc->FaultHoldingCnt = 0;
    Operastruc->reset_cnt = 0;

    Operastruc->FaultClean=FALSE;
    Operastruc->FaultwaitCnt=0;

    Operastruc->FaultMem = FALSE;
    Operastruc->PoweroffMemmark=FALSE;
}
/******************************************************************************
* Function Name : Poweroperation_Process
* Description   : ���µ�����߼�
* Input         : �Ӽ��ٿ��ƽṹ��ָ�롢ADC�����ṹ��ָ�롢���µ���ƽṹ��ָ�롢����״̬��ת��PID������ָ�롢ʵ��ת��
* Output        : None
* Return        : None
******************************************************************************/
void Poweroperation_Process(Speedrampctr_Type *SpeedRamp,ADCStruc_Type *ADCStruc,\
                            Powercontrol_Type *PowerOper,Runningstatus_Type *Runningstatus,\
                            PID_Structure_Type *Speedpid,_iq24 Speedact)
{
    //��ֵĸ�ߵ�ѹ����
    PowerOper->DCVoltAver = _IQMPY_MACRO(PowerOper->DCVoltAver,PowerOper->DCVoltAverSizeInv2)\
                            + _IQMPY_MACRO(ADCStruc->DCVoltPres,PowerOper->DCVoltAverSizeInv1);
//���������ж�
    if((_IQabs(SpeedRamp->Aftramp) >= SpeedRamp->Startfinishspeed)&&(_IQabs(Speedact) >= SpeedRamp->Startfinishspeed))
        SpeedRamp->Startfinish = TRUE;

//�µ��߼�
    if( PowerOper->DCVoltAver <= PowerOper->Poweroff_Volt2)//�ڶ�����ֱ��ͣ������ֹ�͵�ѹӲ�����ȶ�
    {
        PowerOper->Poweron = FALSE;
    }
    if(PowerOper->Poweron == TRUE)
    {
        if(( PowerOper->DCVoltAver < PowerOper->Poweroff_Volt1)&&( PowerOper->DCVoltAver > PowerOper->Poweroff_Volt2))//��һ�������ټ���
        {
            if(*Runningstatus == RUN)
            {
              if(_IQabs(SpeedRamp->Aftramp)>_IQabs(Speedact))//�µ�����п��ܻ��л���һ��ת�� ���ʱ��ֱ�ӽ�ת��û�б�Ҫ��ֵ
                SpeedRamp->Aftramp = Speedact;
              SpeedRamp->Preramp = SpeedRamp->Faststopspeed;
              Speedpid->Output_New = 0;
              Speedpid->Output_Prev = 0;
              PowerOper->PoweroffBrake=TRUE;
            }
            if(*Runningstatus == START)
                PowerOper->PoweroffBrake=TRUE;

            PowerOper->Poweron = FALSE;
        }
    }
 //�ϵ��߼�
    if( (PowerOper->DCVoltAver > PowerOper->Poweron_Volt)&&(SpeedRamp->Break==FALSE)&&((PowerOper->PoweroffBrake==FALSE)))
    {
        PowerOper->Poweron_Cnt++;
        if(PowerOper->Poweron_Cnt >= PowerOper->Poweron_CntThresh)
        {
            PowerOper->Poweron = TRUE;
            PowerOper->Poweron_Cnt = 0;
        }
    }
}
/******************************************************************************
* Function Name  : Break_Verify
* Description    : �ƶ��ж�
* Input          : ���µ���ƽṹ��ָ�룬�Ӽ��ٿ��ƽṹ��ָ�롢ϵͳ����״̬
* Output         : None
* Return         : None
*****************************************************************************/
__attribute__((section(".highcode")))
void Break_Verify(Powercontrol_Type *PowerOper,Speedrampctr_Type *Speed_Ramp,\
                  Runningstatus_Type *Runningstatus,ADCStruc_Type *ADCStruc)
{
    switch(*Runningstatus)
    {
        case START:
            if((Speed_Ramp->Break == FALSE)&&((PowerOper->Poweron==FALSE)||(Speed_Ramp->BreakStop == TRUE)))
            {
                   Speed_Ramp->Break =TRUE;
                   Speed_Ramp->Breakwait_cnt = Speed_Ramp->Breakwait_cntset;
             }
            break;
        case RUN:
            if(Speed_Ramp->Break == FALSE)
            {
            #ifdef POWERON_START
                        //����1-������ɺ󴥷����ټ���&&((��������&&�����µ�״̬)||(�ƶ���־����))
                        //����2-�ϵ������µ����������û�н���(���ֵĿ����Ժ�С)
                        if(((_IQabs(Speed_Ramp->Aftramp) <= Speed_Ramp->Breakspeed)&&\
                           (((Speed_Ramp->Startfinish == TRUE)&&(ADCStruc->DCVoltPres<PowerOper->Poweroff_Volt1))||\
                            (Speed_Ramp->BreakStop == TRUE)))\
                        ||((PowerOper->Poweron==FALSE)&&(Speed_Ramp->Startfinish == FALSE))\
                        ||((PowerOper->Poweron==FALSE)&&(_IQabs(Speed_Ramp->Aftramp) <= Speed_Ramp->Breakspeed))
                        ||((PowerOper->Poweron==FALSE)&&(ADCStruc->DCVoltPres <= PowerOper->Poweroff_Volt2)))
                        {
                            Speed_Ramp->Break =TRUE;
                            Speed_Ramp->Breakwait_cnt = Speed_Ramp->Breakwait_cntset;
                        }

            #else
                    //����1-������ɺ󴥷����ټ���&&��������
                    //����2-�ϵ������µ����������û�н���(���ֵĿ����Ժ�С)
                    //����3-�µ磬ת��С���ƶ�ת��
                    //����4-�µ磬��ѹС���µ��ѹ2
                    if(((_IQabs(Speed_Ramp->Aftramp) <= Speed_Ramp->Breakspeed)&&((Speed_Ramp->Startfinish == TRUE)||(Speed_Ramp->BreakStop == TRUE)))\
                            ||((PowerOper->Poweron==FALSE)&&(Speed_Ramp->Startfinish == FALSE))\
                            ||((PowerOper->Poweron==FALSE)&&(_IQabs(Speed_Ramp->Aftramp) <= Speed_Ramp->Breakspeed))\
                            ||((PowerOper->Poweron==FALSE)&&(ADCStruc->DCVoltPres <= PowerOper->Poweroff_Volt2)))
                    {
                        Speed_Ramp->Break =TRUE;
                        Speed_Ramp->Breakwait_cnt = Speed_Ramp->Breakwait_cntset;
                    }
            #endif
            }
            break;

        default:
            break;
    }
    if((Speed_Ramp->Break == TRUE)&&(Speed_Ramp->Breakwait_cnt==0))
    {
        if( *Runningstatus != FAULT)//��ֹɲ�����̽���FAULT�����STOP���³����쳣
         *Runningstatus = PRESTOP;

        Speed_Ramp->Break =FALSE;
        PowerOper->PoweroffBrake=FALSE;
    }
}
