/* Define to prevent recursive inclusion ------------------------------------*/
#ifndef __M0_CONTROL_LIBRAYR_H
#define __M0_CONTROL_LIBRAYR_H

/* Includes -----------------------------------------------------------------*/
#include "ch32l103.h"
#include "ch32l103_opa.h"
#include "debug.h"
#include "M0_type_and_Constant.h"
#include "M0_Control_Configure.h"
#include "M1_Motor_Parameter.h"
#include "M2_PID_Parameter.h"
#include "M3_LBG_Observer_Parameter.h"
#include "M4_SVPWM_ADC1R.h"
#include "M5_Operation_and_Display.h"
#include "M5_Operation_KeyandLed.h"
#include "M5_Operation_TRIAC.h"
#include "M5_MotorStart.h"
#include "M6_SpeedLoop.h"
#include "M6_CurrentLoop.h"
#include "M7_Interrupt.h"
#include "M8_Protection.h"
#include "M9_Appication_Blocks.h"
#include "MA_HFVQI_Parameter.h"
#include "MB_Operation_Flash.h"
#include "Motor_SPI_USART_MonitorData.h"
/* Exported types -----------------------------------------------------------*/
/* Exported constants -------------------------------------------------------*/
/* Exported variables -------------------------------------------------------*/
/* Exported macro -----------------------------------------------------------*/
/* Exported functions -------------------------------------------------------*/

#endif
