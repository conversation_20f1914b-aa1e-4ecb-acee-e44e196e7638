/* Includes -----------------------------------------------------------------*/
#include "M0_Control_Library.h"
/* Private typedef ----------------------------------------------------------*/
/* Private variables --------------------------------------------------------*/
/* Variables ----------------------------------------------------------------*/
Led_Manager_Type    LED_M;              //LED�����ṹ��
Key_Manager_Type    Key_M;              //���������ṹ��
/******************************************************************************
* Function Name  : KEY_AND_LEDIO_Init
* Description    : ������ָʾ��IO����
* Input          : None
* Output         : None
* Return         : None
******************************************************************************/
void KeyandLED_IOInit(void)
{
    //GPIO��ʼ���ýṹ�嶨��
    GPIO_InitTypeDef GPIO_InitStructure={0};
    //ʱ��ʹ��
    RCC_PB2PeriphClockCmd(RCC_PB2Periph_AFIO|RCC_PB2Periph_GPIOA|\
            RCC_PB2Periph_GPIOB|RCC_PB2Periph_GPIOC, ENABLE);
    GPIO_PinRemapConfig(GPIO_Remap_SWJ_Disable, ENABLE);

    //KEY1:PA14 KEY2:PA13 KEY3:PB11 ZERO:PA3
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11;               //�˿ڶ���
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;            //�˿�ģʽ���壬IO������
    GPIO_Init(GPIOB, &GPIO_InitStructure);                   //�Ĵ���ʵ������

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3|GPIO_Pin_13|GPIO_Pin_14;   //�˿ڶ���
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;            //�˿�ģʽ���壬IO������
    GPIO_Init(GPIOA, &GPIO_InitStructure);                   //�Ĵ���ʵ������


    //LED1:PB8 LED2:PA1 LED3:PA11 SW_WARN:PB7
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;                //�˿ڶ���
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;            //�˿�ģʽ���壬IO��ģ�������ֹ�ϵ���LED��˸
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;        //������Ƶ�ʶ���
    GPIO_Init(GPIOB, &GPIO_InitStructure);                   //�Ĵ���ʵ������

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0|GPIO_Pin_11;    //�˿ڶ���
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;            //�˿�ģʽ���壬IO��ģ������
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;        //������Ƶ�ʶ���
    GPIO_Init(GPIOA, &GPIO_InitStructure);                   //�Ĵ���ʵ������

    GPIO_SetBits(GPIOB, GPIO_Pin_7);
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_7;                //�˿ڶ���
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;         //�˿�ģʽ���壬IO������
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;        //������Ƶ�ʶ���
    GPIO_Init(GPIOB, &GPIO_InitStructure);                   //�Ĵ���ʵ������
    GPIO_SetBits(GPIOB, GPIO_Pin_7);
}
/******************************************************************************
* Function Name  : KeyandLED_Parameter_Define
* Description    : ת�ٺ�LED��������
* Input          : ���������ṹ��ָ�룬LED�����ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void KeyandLED_Parameter_Define(Key_Manager_Type *KeyM,Led_Manager_Type *LEDM)
{
//    KeyM->SHkey_checkinterval = (uint16_t)(SHKEY_CHECK_INTERVAL*KEY_CHECK_FREQ);
//    KeyM->SHkey_effcnt_thresh = (uint16_t)(SHKEY_EFFECTIVE_TIME*KEY_CHECK_FREQ);
//    KeyM->SHkey_ineff_cntthresh = (uint16_t)(SHKEY_INEFFECTIVE_TIME*KEY_CHECK_FREQ);
    KeyM->SHkey_Speedset=FALSE;

    KeyM->SRkey_effcnt_thresh = (uint16_t)(SRKEY_EFFECTIVE_TIME*KEY_CHECK_FREQ);
    KeyM->SRkey_ineff_cntthresh = (uint16_t)(SRKEY_INEFFECTIVE_TIME*KEY_CHECK_FREQ);

    KeyM->Tkey_rankset = TKEY_RANK_SET;
    KeyM->Tkey_effcnt_thresh  = (uint16_t)(TKEY_EFFECTIVE_TIME*KEY_CHECK_FREQ);
    KeyM->Tkey_ineff_cntthresh  = (uint16_t)(TKEY_INEFFECTIVE_TIME*KEY_CHECK_FREQ);

    KeyM->TCkey_effcnt_thresh=(uint16_t)(TCKEY_EFFECTIVE_TIME*KEY_CHECK_FREQ);
    KeyM->TCkey_ineff_cntthresh=(uint16_t)(TCKEY_INEFFECTIVE_TIME*KEY_CHECK_FREQ);

    KeyM->ACkey_checkinterval = (uint16_t)(ACKEY_CHECK_INTERVAL*KEY_CHECK_FREQ);
    KeyM->ACkey_effcnt_thresh = (uint16_t)(ACKEY_EFFECTIVE_TIME*KEY_CHECK_FREQ);
    KeyM->ACkey_ineff_cntthresh = (uint16_t)(ACKEY_INEFFECTIVE_TIME*KEY_CHECK_FREQ);
    KeyM->ACkey_checkcnt = 0;
    KeyM->ACkey_effcnt = 0;
    KeyM->ACkey_ineff_cnt = 0;
    KeyM->ACkey_Poweron = FALSE;

    KeyM->Skey_rank_Dir=DIRUP;
    KeyM->Tkey_rank_Dir=DIRUP;

    KeyM->Skey_rank=0;
    KeyM->Skey_rankset=SPEEDPOINT_RANK;

    KeyM->TCkey_effcnt = 0;
    KeyM->TCkey_ineff_cnt = 0;
    KeyM->TCkeycold= FALSE;

    LEDM->FlickInterval = (uint16_t)(LED_PROCESS_FREQ*LED_FLICKER_TIME);

    LEDM->LED4_LightFlag=OFF;
    LEDM->LED5_LightFlag=OFF;
    LEDM->LED6_LightFlag=OFF;
    LEDM->LED7_LightFlag=OFF;
    LEDM->LED8_LightFlag=OFF;
    LEDM->LED9_LightFlag=OFF;

    LEDM->PSC.Ledinttimeset = (uint16_t)(PSC_LED_INTERVAL*PSC_FREQ);
    LEDM->PSC.Ledintnumset = PSC_LED_INTERVAL_NUM;
    LEDM->PSC.Ledintnumtotalset = (uint16_t)(PSC_LED_INTERVAL_TOTAL/PSC_LED_INTERVAL);
    LEDM->PSC.Ledlasttimeset = (uint16_t)(PSC_LED_LAST*PSC_FREQ);

    LEDM->PSC.Mark = FALSE;
    LEDM->PSC.Stage = NONE;
    LEDM->PSC.Process = FALSE;
    LEDM->PSC.Ledintdir =DIRUP;


    LEDM->Pin1.Group = GPIOB;
    LEDM->Pin1.Pin = GPIO_Pin_8;

    LEDM->Pin2.Group = GPIOA;
    LEDM->Pin2.Pin = GPIO_Pin_0;

    LEDM->Pin3.Group = GPIOA;
    LEDM->Pin3.Pin = GPIO_Pin_11;
}
/******************************************************************************
* Function Name  : KeyandLED_Status_Ini
* Description    : ת�ٺ�LED״̬��ʼ��
* Input          : ���������ṹ��ָ�룬LED�����ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void KeyandLED_Status_Ini(Key_Manager_Type *KeyM,Led_Manager_Type *LEDM)
{
//    KeyM->SHkey_checkcnt = 0;
//    KeyM->SHkey_effcnt1 = 0;
//    KeyM->SHkey_ineff_cnt1 = 0;
//    KeyM->SHkey_effcnt2 = 0;
//    KeyM->SHkey_ineff_cnt2 = 0;

    KeyM->SRkey_effcnt =0;
    KeyM->SRkey_ineff_cnt =0;

    KeyM->Tkey_effcnt =0;
    KeyM->Tkey_ineff_cnt =0;

    KeyM->Skeystop = FALSE;
    KeyM->Skeystopcnt = 0;

    KeyM->TCkey_effcnt = 0;
    KeyM->TCkey_ineff_cnt = 0;
    KeyM->TCkeycold= FALSE;

    LEDM->FlickCnt = 0;
    LEDM->FlickCnt = 0;

    LEDM->PSC.Ledcnt = 0;
    LEDM->PSC.Ledintnum = 0;
    LEDM->PSC.Ledintnumtotal = 0;
}
/******************************************************************************
* Function Name  : SpeedLed_Set
* Description    : ת��LED����
* Input          : ���������ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void SpeedLed_Set(Key_Manager_Type *KeyM)
{
    switch(Key_M.Skey_rank)
       {
           case 1://ת�ٵ�λ1
               LED_M.LED7_LightFlag=ON;
               LED_M.LED8_LightFlag=OFF;
               LED_M.LED9_LightFlag=OFF;
              break;
          case 2://ת�ٵ�λ2
              LED_M.LED7_LightFlag=ON;
              LED_M.LED8_LightFlag=ON;
              LED_M.LED9_LightFlag=OFF;
              break;
          case 3://ת�ٵ�λ3
              LED_M.LED7_LightFlag=ON;
              LED_M.LED8_LightFlag=ON;
              LED_M.LED9_LightFlag=ON;
              break;
          default:
              break;
       }
}
/******************************************************************************
* Function Name  : TempLed_Set
* Description    : �¶�LED����
* Input          : ���������ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void TempLed_Set(Key_Manager_Type *KeyM)
{
    switch(Key_M.Tkey_rank)
       {
           case 0://�¶ȵ�λ1
               LED_M.LED4_LightFlag=OFF;
               LED_M.LED5_LightFlag=OFF;
               LED_M.LED6_LightFlag=OFF;
               break;
           case 1://�¶ȵ�λ1
               LED_M.LED4_LightFlag=OFF;
               LED_M.LED5_LightFlag=OFF;
               LED_M.LED6_LightFlag=ON;
              break;
          case 2://�¶ȵ�λ2
              LED_M.LED4_LightFlag=OFF;
              LED_M.LED5_LightFlag=ON;
              LED_M.LED6_LightFlag=ON;
              break;
          case 3://�¶ȵ�λ3
              LED_M.LED4_LightFlag=ON;
              LED_M.LED5_LightFlag=ON;
              LED_M.LED6_LightFlag=ON;
              break;
          default:
              break;
       }
}

/******************************************************************************
* Function Name : LED_Reset
* Description   : LED��λ
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void LED_Reset(Led_Manager_Type *LEDM)
{
       LEDM->Pin1.Group->CFGHR&=~(0xf<<(4*0));//PB8
       LEDM->Pin2.Group->CFGLR&=~(0xf<<(4*0));//PA0
       LEDM->Pin3.Group->CFGHR&=~(0xf<<(4*3));//PA11

       LEDM->Pin1.Group->CFGHR|=(0x4<<(4*0));//PB8
       LEDM->Pin2.Group->CFGLR|=(0x4<<(4*0));//PA0
       LEDM->Pin3.Group->CFGHR|=(0x4<<(4*3));//PA11
}
/******************************************************************************
* Function Name : LED4_ON
* Description   : LED4ON
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void LED4_ON(Led_Manager_Type *LEDM)
{
    LEDM->Pin1.Group->CFGHR&=~(0xf<<(4*0));//PB8
    LEDM->Pin1.Group->CFGHR|=0x3<<(4*0);
//    GPIO_SetBits( LEDM->Pin1.Group,LEDM->Pin1.Pin);
    LEDM->Pin1.Group->BSHR = LEDM->Pin1.Pin;
}

/******************************************************************************
* Function Name : LED5_ON
* Description   : LED5ON
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void LED5_ON(Led_Manager_Type *LEDM)
{
    LEDM->Pin1.Group->CFGHR&=~(0xf<<(4*0));//PB8
    LEDM->Pin1.Group->CFGHR|=0x3<<(4*0);
//    GPIO_ResetBits( LEDM->Pin1.Group,LEDM->Pin1.Pin);
    LEDM->Pin1.Group->BCR = LEDM->Pin1.Pin;
}
/******************************************************************************
* Function Name : LED6_ON
* Description   : LED6ON
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void LED6_ON(Led_Manager_Type *LEDM)
{
    LEDM->Pin2.Group->CFGLR&=~(0xf<<(4*0));//PA0
    LEDM->Pin2.Group->CFGLR|=0x3<<(4*0);
    //GPIO_ResetBits( LEDM->Pin2.Group,LEDM->Pin2.Pin);
    LEDM->Pin2.Group->BCR = LEDM->Pin2.Pin;
}
/******************************************************************************
* Function Name : LED7_ON
* Description   : LED7ON
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void LED7_ON(Led_Manager_Type *LEDM)
{
    LEDM->Pin2.Group->CFGLR&=~(0xf<<(4*0));//PA0
    LEDM->Pin2.Group->CFGLR|=0x3<<(4*0);
    //GPIO_SetBits( LEDM->Pin2.Group,LEDM->Pin2.Pin);
    LEDM->Pin2.Group->BSHR = LEDM->Pin2.Pin;
}
/******************************************************************************
* Function Name : LED8_ON
* Description   : LED8ON
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void LED8_ON(Led_Manager_Type *LEDM)
{
    LEDM->Pin3.Group->CFGHR&=~(0xf<<(4*3));//PA11
    LEDM->Pin3.Group->CFGHR|=0x3<<(4*3);
    //GPIO_ResetBits( LEDM->Pin3.Group,LEDM->Pin3.Pin);
    LEDM->Pin3.Group->BCR = LEDM->Pin3.Pin;
}
/******************************************************************************
* Function Name : LED9_ON
* Description   : LED9ON
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void LED9_ON(Led_Manager_Type *LEDM)
{
    LEDM->Pin3.Group->CFGHR&=~(0xf<<(4*3));//PA11
    LEDM->Pin3.Group->CFGHR|=0x3<<(4*3);
    //GPIO_SetBits( LEDM->Pin3.Group,LEDM->Pin3.Pin);
    LEDM->Pin3.Group->BSHR = LEDM->Pin3.Pin;
}
/******************************************************************************
* Function Name : Led_OFF
* Description   : Led_OFF
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void Led_OFF(Led_Manager_Type *LEDM)
{
    LEDM->LED4_LightFlag=OFF;
    LEDM->LED5_LightFlag=OFF;
    LEDM->LED6_LightFlag=OFF;
    LEDM->LED7_LightFlag=OFF;
    LEDM->LED8_LightFlag=OFF;
    LEDM->LED9_LightFlag=OFF;
}

/******************************************************************************
* Function Name : Led_Operation
* Description   : LED����
* Input         : LED����ָ��
* Output        : None
* Return        : None
******************************************************************************/
void Led_Operation(Led_Manager_Type *LEDM)
{
    static u8 Led_cnt;
    Led_cnt++;
    if(Led_cnt>2)
        Led_cnt=1 ;

    LED_Reset(LEDM);
    if(SpeedRamp_M.Break == FALSE)
    {
        switch(Led_cnt)
        {
            case 1:
                if(((LEDM->LED5_LightFlag)&&(Key_M.TCkeycold==FALSE))||((LEDM->LED5_LightFlag)&&(Tempctl_M.NTCFlickerStatus ==TRUE))||((LEDM->LED5_LightFlag)&&(RunningStatus_M ==FAULT)))
                    LED5_ON(LEDM);

                if(((LEDM->LED6_LightFlag)&&(Key_M.TCkeycold==FALSE))||((LEDM->LED6_LightFlag)&&(Tempctl_M.NTCFlickerStatus ==TRUE))||((LEDM->LED6_LightFlag)&&(RunningStatus_M ==FAULT)))
                    LED6_ON(LEDM);

                if(LEDM->LED8_LightFlag)
                    LED8_ON(LEDM);
                break;
            case 2:
                if(((LEDM->LED4_LightFlag)&&(Key_M.TCkeycold==FALSE))|| ((LEDM->LED4_LightFlag)&&(Tempctl_M.NTCFlickerStatus ==TRUE))||((LEDM->LED4_LightFlag)&&(RunningStatus_M ==FAULT)) )//�¶�
                    LED4_ON(LEDM);

                if(LEDM->LED7_LightFlag)
                    LED7_ON(LEDM);

                if(LEDM->LED9_LightFlag)
                    LED9_ON(LEDM);
        }
    }

}
/******************************************************************************
* Function Name  : LED_Flicker
* Description    : LED��˸����
* Input          : LED�ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void LED_Flicker(Led_Manager_Type *LedM)
{
    if (Time_Delay_Elapse(LedM->FlickCnt) == TRUE)
        {
            LedM->FlickCtl++;
            Time_Delay_Set(LedM->FlickInterval,&(LedM->FlickCnt));

            if(LedM->FlickCtl == 1)
            {
                LED_M.LED4_LightFlag=ON;
                LED_M.LED5_LightFlag=ON;
                LED_M.LED6_LightFlag=ON;

                LED_M.LED7_LightFlag=OFF;
                LED_M.LED8_LightFlag=OFF;
                LED_M.LED9_LightFlag=OFF;
            }
            if(LedM->FlickCtl == 2)
            {
               LED_M.LED4_LightFlag=OFF;
               LED_M.LED5_LightFlag=OFF;
               LED_M.LED6_LightFlag=OFF;

               LED_M.LED7_LightFlag=OFF;
               LED_M.LED8_LightFlag=OFF;
               LED_M.LED9_LightFlag=OFF;
               LedM->FlickCtl =0;
            }
        }
}
/******************************************************************************
* Function Name  : Coldkeycheck
* Description    : һ�������־
* Input          : ���������ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void Coldkeycheck(Key_Manager_Type *KeyM)
{
    //1�����
    if(GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_11) == 0)//����⣬�͵�ƽ��Ч
        KeyM->TCkey_effcnt++;
    else
        KeyM->TCkey_ineff_cnt++;

    if(KeyM->TCkey_effcnt >= KeyM->TCkey_effcnt_thresh)//���
    {
        KeyM->TCkeycold=TRUE;
        KeyM->TCkey_effcnt = 0;
        KeyM->TCkey_ineff_cnt = 0;
    }
    else if(KeyM->TCkey_ineff_cnt >= KeyM->TCkey_ineff_cntthresh)//�ȷ�
    {
        KeyM->TCkeycold=FALSE;
        KeyM->TCkey_effcnt = 0;
        KeyM->TCkey_ineff_cnt = 0;
    }
}
/******************************************************************************
* Function Name  : Tempkeycheck
* Description    : �¿ؼ����
* Input          : ���������ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void Tempkeycheck(Key_Manager_Type *KeyM)
{
    //�¶Ȱ������
    if(GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_14) == 0)//��������
    {
        if(KeyM->Tkey_effcnt< U16_MAX)
            KeyM->Tkey_effcnt++;
    }
    else    //�����ͷ�
    {
        KeyM->Tkey_ineff_cnt++;
    }

    if(KeyM->Tkey_ineff_cnt >= KeyM->Tkey_ineff_cntthresh)//�����Ѿ��ͷ�
    {
        if(KeyM->Tkey_effcnt >= KeyM->Tkey_effcnt_thresh)
        {
            if((RunningStatus_M == RUN)&&(Tempctl_M.NTCOverStatus==FALSE)&&\
               (Tempctl_M.NTCOpenStatus==FALSE))//����״̬ ������ NTC����·
             {
                if(KeyM->Tkey_rank >= KeyM->Tkey_rankset)//����ߵ�����������ݼ�
                    KeyM->Tkey_rank_Dir = DIRDOWN;
                else if (KeyM->Tkey_rank <=0)//����͵��������������
                    KeyM->Tkey_rank_Dir = DIRUP;

                if(KeyM->Tkey_rank_Dir == DIRUP)
                    KeyM->Tkey_rank ++;
                else if(KeyM->Tkey_rank_Dir == DIRDOWN)
                    KeyM->Tkey_rank --;

                    TempLed_Set(KeyM);

                    Triac_ctl.PeriodCtl.DutychangeFlag=TRUE;
             }
        }
        KeyM->Tkey_effcnt = 0;
        KeyM->Tkey_ineff_cnt = 0;
    }
}
///******************************************************************************
//* Function Name  : Speedkeycheck_Hold
//* Description    : ת�ٰ������_�Ƶ�������
//* Input          : ���������ṹ��ָ�룬�Ӽ��ٿ��ƽṹ��
//* Output         : None
//* Return         : None
//******************************************************************************/
//void Speedkeycheck_Hold(Key_Manager_Type *KeyM,Speedrampctr_Type *Speed_Ramp)
//{
//    KeyM->SHkey_checkcnt++;
//
//    //1�����
//    if(GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_1) == 0)//1����⣬�͵�ƽ��Ч
//        KeyM->SHkey_effcnt1++;
//    else
//        KeyM->SHkey_ineff_cnt1++;
//
//    //2�����
//    if(GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_11) == 0)//2����⣬�͵�ƽ��Ч
//        KeyM->SHkey_effcnt2++;
//    else
//        KeyM->SHkey_ineff_cnt2++;
//
//    if (KeyM->SHkey_checkcnt >= KeyM->SHkey_checkinterval)
//    {
//        if(KeyM->SHkey_effcnt2 >= KeyM->SHkey_effcnt_thresh)//ʱ�������ֵ���趨��λ2
//        {
//            if((KeyM->ACkey_Poweron==TRUE)&&(Powerctr_M.Poweron == TRUE))
//            {
//                if( KeyM->Skey_rank !=2)
//                {
//                    KeyM->Skey_rank = 2;
//                    KeyM->SHkey_Speedset= TRUE;
//                }
//            }
//        }
//        else if(KeyM->SHkey_effcnt1 >= KeyM->SHkey_effcnt_thresh)//ʱ�������ֵ���趨��λ1
//        {
//            if((KeyM->ACkey_Poweron==TRUE)&&(Powerctr_M.Poweron == TRUE))
//            {
//                if( KeyM->Skey_rank != 1)
//                {
//                    KeyM->Skey_rank = 1;
//                    KeyM->SHkey_Powerdown = FALSE;//��������
//                    KeyM->SHkey_Speedset= TRUE;
//                }
//            }
//        }
//        KeyM->SHkey_checkcnt = 0;//����������
//        KeyM->SHkey_effcnt1 = 0;
//        KeyM->SHkey_ineff_cnt1 = 0;
//        KeyM->SHkey_effcnt2 = 0;
//        KeyM->SHkey_ineff_cnt2 = 0;
//    }
//    switch(RunningStatus_M)
//    {
//        case PRESTART:
//        case POSITION:
//        case START:
//        case RUN:
//        if(Powerctr_M.Poweron == TRUE)
//        {
//            if((KeyM->Skeystop == TRUE)||(Operation_M.Faultstop == TRUE))//����ͣ���͹���ͣ��
//            {
//                if(KeyM->Skeystopcnt<U8_MAX)
//                    KeyM->Skeystopcnt ++;
//
//                if(KeyM->Skeystopcnt <=1)//ִֻ��һ��
//                {
//                      if(RunningStatus_M == RUN)
//                      {
//                         if(_IQabs(Speed_Ramp->Aftramp) > _IQabs(MLBG_M.Cal_Struc.Angspeed_Aver2))
//                         Speed_Ramp->Aftramp = MLBG_M.Cal_Struc.Angspeed_Aver2;//Speed_Ramp->Aftramp<MLBG_M.Cal_Struc.Angspeed_Aver2���ʱ��ֱ�ӽ�ת��û�б�Ҫ��ֵ
//                         Speed_Ramp->Preramp = Speed_Ramp->Faststopspeed;
//                         Mpid_M.Speed.Output_New = 0;
//                         Mpid_M.Speed.Output_Prev = 0;
//                         Speed_Ramp->BreakStop = TRUE;
//                      }
//                      else if(RunningStatus_M == START)
//                      {
//                          Speed_Ramp->BreakStop = TRUE;
//                      }
//                      else
//                          RunningStatus_M = STOP;
//                }
//            }
//            if((KeyM->SHkey_Speedset==TRUE))//������Ч�Ҷ��Ӷ�·�ƶ�ֹͣ״̬ΪFALSE
//            {
//                if((KeyM->Skeystop == FALSE)&&(Operation_M.Faultstop == FALSE))//����ͣ��
//                {
//                    Speed_Set(Speed_Ramp);
//                }
//                KeyM->SHkey_Speedset=FALSE;
//            }
//        }
//        break;
//        default:
//            break;
//    }
//    if((KeyM->ACkey_Poweron==FALSE)||(Powerctr_M.Poweron == FALSE))//�ϵ��־������� �������ϵ��־�Ͽ�
//    {
//        KeyM->Skey_rank = 0;
//        KeyM->SHkey_Speedset = FALSE;//�µ�������λ���ñ�־
//    }
//}
/******************************************************************************
* Function Name  : Speedkeycheck_Recover
* Description    : ת�ٰ������_�ָ�ѭ����
* Input          : ���������ṹ��ָ�룬�Ӽ��ٿ��ƽṹ��
* Output         : None
* Return         : None
******************************************************************************/
void Speedkeycheck_Recover(Key_Manager_Type *KeyM,Speedrampctr_Type *Speed_Ramp)
{
    //�������
    if(GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_13) == 0)//�͵�ƽ��Ч
        KeyM->SRkey_effcnt++;
    else
        KeyM->SRkey_ineff_cnt++;

    if(KeyM->SRkey_ineff_cnt >= KeyM->SRkey_ineff_cntthresh)//�����Ѿ��ͷ�
    {
        if(KeyM->SRkey_effcnt >= KeyM->SRkey_effcnt_thresh)
         {
          #if defined POWERON_START       //�ϵ���������ѭ��Ϊ1��
            if(((RunningStatus_M==RUN)||(RunningStatus_M==START))&&\
               (Powerctr_M.Poweron == TRUE)&&(KeyM->ACkey_Poweron == TRUE)&&(Operation_M.Faultstop==FALSE))
                {
                    if(KeyM->Skey_rank >= KeyM->Skey_rankset)//����ߵ�����������ݼ�
                        KeyM->Skey_rank_Dir = DIRDOWN;
                    else if (KeyM->Skey_rank <=1)//����͵��������������
                        KeyM->Skey_rank_Dir = DIRUP;

                    if(KeyM->Skey_rank_Dir == DIRUP)
                        KeyM->Skey_rank ++;
                    else if(KeyM->Skey_rank_Dir == DIRDOWN)
                        KeyM->Skey_rank --;

                    KeyM->SHkey_Speedset= TRUE;
                    Triac_ctl.PeriodCtl.DutychangeFlag=TRUE;
                }

                #elif defined  KEY_START
                if((RunningStatus_M==START)||(RunningStatus_M==RUN)||(RunningStatus_M==IDLE))
                {
                    KeyM->Skey_rank ++;
                    if(KeyM->Skey_rank > KeyM->Skey_rankset)
                    {
                        KeyM->Skey_rank = 0;
                        KeyM->Skeystop = TRUE;
                    }
                    else
                    {
                        Triac_ctl.PeriodCtl.DutychangeFlag=TRUE;
                    }
                    KeyM->SHkey_Speedset=TRUE;
                }
                #endif
        }
        KeyM->SRkey_effcnt = 0;
        KeyM->SRkey_ineff_cnt = 0;
    }
    switch(RunningStatus_M)
    {
        case PRESTART:
        case POSITION:
        case START:
        case RUN:
        if(Powerctr_M.Poweron == TRUE)
        {
            if(KeyM->SHkey_Speedset==TRUE)
            {
                if(Operation_M.Faultstop==FALSE)//����ͣ��
                {
                    Speed_Set(Speed_Ramp);
                    SpeedLed_Set(KeyM);
                }
                KeyM->SHkey_Speedset=FALSE;
            }
            if((KeyM->Skeystop == TRUE)||(Operation_M.Faultstop == TRUE))//����ͣ���͹���ͣ��
            {
                if(KeyM->Skeystopcnt<U8_MAX)
                    KeyM->Skeystopcnt ++;

                if(KeyM->Skeystopcnt <=1)//ִֻ��һ��
                {
                      if(RunningStatus_M == RUN)
                      {
                         if(_IQabs(Speed_Ramp->Aftramp) > _IQabs(MLBG_M.Cal_Struc.Angspeed_Aver2))
                         Speed_Ramp->Aftramp = MLBG_M.Cal_Struc.Angspeed_Aver2;//Speed_Ramp->Aftramp<MLBG_M.Cal_Struc.Angspeed_Aver2���ʱ��ֱ�ӽ�ת��û�б�Ҫ��ֵ
                         Speed_Ramp->Preramp = Speed_Ramp->Faststopspeed;
                         Mpid_M.Speed.Output_New = 0;
                         Mpid_M.Speed.Output_Prev = 0;
                         Speed_Ramp->BreakStop = TRUE;
                      }
                      else if(RunningStatus_M == START)
                      {
                          Speed_Ramp->BreakStop = TRUE;
                      }
                      else
                          RunningStatus_M = STOP;
                }
            }
        }
        break;
        default:
            break;
    }
    if((KeyM->ACkey_Poweron==FALSE)||(Powerctr_M.Poweron == FALSE))//�ϵ��־������� �������ϵ��־�Ͽ�
    {
    #if defined KEY_START
        KeyM->Skey_rank = 0;
    #endif
        KeyM->SHkey_Speedset=FALSE;
    }
}
/******************************************************************************
* Function Name  : Speed_Set
* Description    : ת���趨
* Input          : �Ӽ��ٿ��ƽṹ��ָ�롢���µ���ƽṹ��ָ��
* Output         : None
* Return         : None
*****************************************************************************/
void Speed_Set(Speedrampctr_Type *Speed_Ramp)
{
    switch(Key_M.Skey_rank)
    {
        case 1://ת�ٵ�λ1
           Speed_Ramp->Preramp = SPEEDPOINT1;
           break;
       case 2://ת�ٵ�λ2
           Speed_Ramp->Preramp = SPEEDPOINT2;
           break;
       case 3://ת�ٵ�λ3
           Speed_Ramp->Preramp = SPEEDPOINT3;
           break;
       default:
           break;
    }
}
/******************************************************************************
* Function Name  : ACON_CHECK
* Description    : �����ϵ���
* Input          : ���������ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void ACKEY_CHECK(Key_Manager_Type *KeyM)
{
    KeyM->ACkey_checkcnt++;

    //AC�ϵ�
    if(GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_3) == 0)//
        KeyM->ACkey_effcnt++;
    else
        KeyM->ACkey_ineff_cnt++;

    if (KeyM->ACkey_checkcnt >= KeyM->ACkey_checkinterval)
    {
        if(KeyM->ACkey_effcnt >= KeyM->ACkey_effcnt_thresh)//ʱ�������ֵ���ϵ�
        {
            KeyM->ACkey_Poweron = TRUE;//��������
        }
        else if(KeyM->ACkey_ineff_cnt >= KeyM->ACkey_ineff_cntthresh)//��������
        {
            KeyM->ACkey_Poweron = FALSE;
        }
        KeyM->ACkey_checkcnt = 0;//����������
        KeyM->ACkey_effcnt = 0;
        KeyM->ACkey_ineff_cnt = 0;
    }
}
/******************************************************************************
* Function Name  : PSC_Process
* Description    : �ϵ��Լ촦��
* Input          : ���������ṹ��ָ�룬LED�����ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void PSC_Process(Led_Manager_Type *LEDM)
{
    switch (LEDM->PSC.Stage)
    {
        case NONE:
#ifdef  POWERONCHECK
            if((GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_14) == 0)&&(GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_13)== 0)&&(Key_M.ACkey_Poweron == FALSE))
            {
                LEDM->PSC.Stage = CHECK;//������̬
                LEDM->PSC.Mark =TRUE;
            }
#endif
            LEDM->PSC.Process = TRUE;//��ֹ

            break;
        case CHECK:
            //�¿ؼ���ת�ٰ�������Ч
            if((GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_14) == 0)&&(GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_13)== 0)&&(Key_M.ACkey_Poweron == TRUE)&&(Powerctr_M.Poweron == TRUE))
            {
                LEDM->PSC.Ledintnum = 0;
                LEDM->PSC.Ledintnumtotal = 0;

                LEDM->PSC.Stage = INTERVAL;//���뽻�����״̬
                Time_Delay_Set(LEDM->PSC.Ledinttimeset,&(LEDM->PSC.Ledcnt));
            }
        break;

        case INTERVAL:
            if(LEDM->PSC.Ledcnt ==0)
            {
                if(LEDM->PSC.Ledintdir == DIRUP)
                {
                    LEDM->PSC.Ledintnum++;
                    if(LEDM->PSC.Ledintnum >= (LEDM->PSC.Ledintnumset-1))
                    {
                        LEDM->PSC.Ledintdir = DIRDOWN;
                    }
                }
                else if(LEDM->PSC.Ledintdir == DIRDOWN)
                {
                    LEDM->PSC.Ledintnum--;
                    if(LEDM->PSC.Ledintnum <= 0)
                    {
                        LEDM->PSC.Ledintdir = DIRUP;
                    }
                }

                LEDM->PSC.Ledintnumtotal++;

                Time_Delay_Set(LEDM->PSC.Ledinttimeset,&(LEDM->PSC.Ledcnt));
            }
            LED_Reset(&LED_M);
            switch(LEDM->PSC.Ledintnum)
            {
                case 0:
                    LED9_ON(&LED_M);
                 break;

                case 1:
                    LED8_ON(&LED_M);
                  break;

                case 2:
                    LED7_ON(&LED_M);
                 break;

                default:
                 break;
            }
            if(LEDM->PSC.Ledintnumtotal >= LEDM->PSC.Ledintnumtotalset)
            {
                LEDM->PSC.Stage = LAST;//�����������״̬
                LED_Reset(&LED_M);
                Time_Delay_Set(LEDM->PSC.Ledlasttimeset,&(LEDM->PSC.Ledcnt));
            }
        break;

        case LAST:
            LED6_ON(&LED_M);
            if(LEDM->PSC.Ledcnt ==0)
            {
                LEDM->PSC.Stage = NONE;//�������̬
                LED_Reset(&LED_M);
            }
        break;

        default:
        break;
    }
}
