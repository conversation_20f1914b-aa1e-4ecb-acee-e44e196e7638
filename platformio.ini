; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:genericCH32L103G8R6]
platform = ch32v
board = genericCH32L103G8R6
framework = noneos-sdk
build_flags = -Llib
              -lIQmath_RV32
              -lM12023_SV_1.00_HVL103G_Core2.0_20240529_Library