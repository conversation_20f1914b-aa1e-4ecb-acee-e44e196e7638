/* Includes -----------------------------------------------------------------*/
#include "M0_Control_Library.h"

/* Private typedef ----------------------------------------------------------*/
/* Private define -----------------------------------------------------------*/
/* Private macro ------------------------------------------------------------*/
/* Private functions --------------------------------------------------------*/
/* Private variables --------------------------------------------------------*/
/* Variables ----------------------------------------------------------------*/
InjStart_Type InjStart_M;  //����������ƽṹ��
/******************************************************************************
* Function Name  : Motor_Start_Init
* Description    : �������ǰ�ĳ�ʼ��
* Input          : None
* Output         : None
* Return         : None
******************************************************************************/
void Motor_Start_Init_M(void)
{
    KeyandLED_Status_Ini(&Key_M,&LED_M);//������ָʾ��״̬��ʼ��

    Operation_Status_Init(&Operation_M);//����״̬��ʼ��

    Speedrampctr_Start_Init_M(&SpeedRamp_M);//ת�ٸ�����ʼ��

    Speed_Set(&SpeedRamp_M);    //ת���趨

    Temp_Set(&Key_M,&Triac_ctl);

    Protection_and_Emergency_Init();//����������״̬��ʼ��

    PID_ParaBase_Cal_M(&MBase_M,&Mpid_M);//PID��������������

    PID_Output_Init(&Mpid_M);//PID�����������ʼ��

    Motor_Status_Initial_M(&MStruc_M);//���״̬��ʼ��

    Injstart_Status_Init_M(&InjStart_M);//�����ջ�������ʼ��

    Dcurr_Process_Init(&DCurrProcess_M,1);//D�����������ʼ��

    SVPWM_1R_Status_Init_M(&SVPWM_Cal_M,&PWMCali_M);//SVPWM����״̬��ʼ��

    LBG_Status_Init(&MLBG_M,&LBG_Status_M);//LBG�۲�����ʼ��

    Buffer_Clear(&(LBG_SpeedBuffer_M[0]),&(MLBG_M.SpeedBuffPara));//LBG�۲�ת�������ʼ��

    HFVQI_Status_Init_M(&HFVQI_StartStruc_M);//HFVQI��λ��ʼ��

    Buffer_Clear(&(HFVQIPosi_CBuff_M[0]),&HFVQIPosi_CBuffPara_M);//HFVQI��λ���������ʼ��

    Powercom_Status_Init(&Powercom_M);//ת�ٲ���������״̬��ʼ��

    ADC_1R_Offset_Reading(ADC1,&ADC_M,1);//ADC1����ƫ�ö�ȡ

    ADC_Status_Init (&ADC_M);//ADC״̬��ʼ��

    Triac_Status_Init(&Triac_ctl,&Tempctl_M);//��������״̬��ʼ��

    SVPWM_Zerovolt_Output_Immediate(&SVPWM_Cal_M);//0��ѹ���

    TIM_CtrlPWMOutputs(TIM1,ENABLE);//ʹ��PWM���

    SpeedLed_Set(&Key_M);

    TempLed_Set(&Key_M);
}
/******************************************************************************
* Function Name  : Injstart_Parameter_Define_M
* Description    : ����ע��������������
* Input          : ����������ƽṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void Injstart_Parameter_Define_M (InjStart_Type *InjStart)
{
    //��λ�׶ε�������������ֵ
    InjStart->ForceInjCurrStep = _IQdiv(FORCE_INJ_CURR_M,FORCE_INJ_CURR_RAMP_TIME_M)/FORCE_INJ_FREQ_M;

    //��λ�׶ε���Ŀ��ֵ��ֵ
    InjStart->ForceInjCurrTarget = FORCE_INJ_CURR_M;

    //��λ�׶ε����Ƕȸ�ֵ
    InjStart->ForceInjCurrAngle = FORCE_INJ_ANGLE_M;

    //��λ�׶θ�ֵ
    InjStart->ForceInjStage = RAMP_INJ_STAGE_M;

    //�����ջ�������������Ŀ��ֵ��ֵ
    InjStart->RampInjCurrTarget = RAMP_INJ_CURR_FINAL_M;

    //�����ջ�������������������ֵ
    InjStart->RampInjCurrStep = _IQdiv((RAMP_INJ_CURR_FINAL_M - RAMP_INJ_CURR_INIT_M),\
                                  RAMP_INJ_CURR_RAMP_TIME_M)/RAMP_INJ_FREQ_M;

    //�����ջ�����ת�ٸ�������ֵ
    InjStart->RampInjSpeedTarget = RAMP_INJ_SPEED_M*STARTUP_DIRECTION;

    //�����ջ�����ת����������1��ֵ
    InjStart->RampInjSpeedStep = _IQdiv(RAMP_INJ_SPEED_M,RAMP_INJ_SPEED_RAMP_TIME_M)/RAMP_INJ_FREQ_M;

    //�����ջ�������ת��ת��Ϊ�Ƕȼ���ϵ��
    InjStart->K_WetoPosi = RAMP_INJ_PERIOD_M*RATED_FREQ_M;

    //�����ջ���������������ж���ֵ
    InjStart->CurrErrThresh = INJECTION_CURRERR_THRESH_M;

    //�����ջ������׶θ�ֵ
    InjStart->RampInjStage1 = RAMP_INJ_STAGE1_M;
    InjStart->RampInjStage2 = RAMP_INJ_STAGE2_M;

    //��·�л�����ֵ��ֵ
    InjStart->Speedadj = RAMP_INJ_SPEED_ADJ*STARTUP_DIRECTION;
    InjStart->Curradj = RAMP_INJ_CURR_ADJ;

    //�����ջ������������Ʋ�������
    InjStart->IF_Vicom.Kcom = IF_VICOM_COEF_M * STARTUP_DIRECTION;
    InjStart->IF_Vicom.Wecomupper = IF_VICOM_UPPER_M;
    InjStart->IF_Vicom.Wecomlower = IF_VICOM_LOWER_M;
    InjStart->IF_Vicom.Power_HPF1st.Input_Coef=_IQdiv(UNIT_Q24,(IF_VICOM_HPF1st_WcT_M + UNIT_Q24));
    InjStart->IF_Vicom.Power_HPF1st.Output_Coef=_IQdiv(UNIT_Q24,(IF_VICOM_HPF1st_WcT_M + UNIT_Q24));
}

/******************************************************************************
* Function Name  : Injstart_Status_Init_M
* Description    : ����ע������״̬��ʼ��
* Input          : ����������ƽṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
void Injstart_Status_Init_M(InjStart_Type *InjStart)
{
    InjStart->ForceInjCurrRef = _IQ(0);                 //ǿ��ע���������
    InjStart->ForceInjCounter = 0;                      //ǿ��ע���������������

    InjStart->RampInjCurrRef = RAMP_INJ_CURR_INIT_M;    //�����ջ�������������ֵ
    InjStart->RampInjSpeedRef = RAMP_INJ_SPEEDINT_M *STARTUP_DIRECTION;    //�����ջ�����ת�ٸ���ֵ
    InjStart->RampInjAngle = _IQ(0);                    //�����ջ������Ƕ�����
    InjStart->RampInjCounter =0;                        //�����ջ���ʱ����������
    InjStart->Curr_2R.cd = _IQ(0);                      //�����ջ������׶ι۲�d���������
    InjStart->Curr_2R.cq = _IQ(0);                      //�����ջ������׶ι۲�q���������
    InjStart->VoltRef_2R.cd = _IQ(0);                   //�����ջ������׶ι۲�d���ѹ����
    InjStart->VoltRef_2R.cq = _IQ(0);                   //�����ջ������׶ι۲�q���ѹ����
    InjStart->Status = INITIAL;                         //����״̬����λΪ��ʼ��״̬

    //�����ջ�������������״̬��ʼ��
    InjStart->IF_Vicom.Powerinst = 0;
    InjStart->IF_Vicom.Powerfilter = 0;
    InjStart->IF_Vicom.Wecom = 0;
    InjStart->IF_Vicom.Power_HPF1st.Out_New = 0;
    InjStart->IF_Vicom.Power_HPF1st.Out_Pre = 0;
    InjStart->IF_Vicom.Power_HPF1st.In_Pre = 0;
}

/******************************************************************************
* Function Name  : Injstart_Process_M
* Description    : ע����������ǿ�ƶ�λ��Ȼ������ջ�����
* Input          : ����������ƽṹ��ָ�룬�������״̬�ṹ��ָ�룬LBG�۲����ṹ��ָ��
* Output         : None
* Return         : None
******************************************************************************/
__attribute__((section(".highcode")))
void Injstart_Process_M(InjStart_Type *InjStart,Motor_Sructure_Type *MStruc,Motor_LBG_Type *MLBG)
{
    switch(InjStart->Status)//����״̬��
    {
        case INITIAL:
            InjStart->Status = INJECTION;
            break;
        case INJECTION://ǿ�ƶ�λ
            if(InjStart->ForceInjCounter < U32_MAX)
                InjStart->ForceInjCounter++;//�����ջ�������ʱ����
            if(InjStart->ForceInjCurrRef < InjStart->ForceInjCurrTarget)
            {
                //��λ�׶Σ�ע���������
                InjStart->ForceInjCurrRef += InjStart->ForceInjCurrStep;
                if(InjStart->ForceInjCurrRef > InjStart->ForceInjCurrTarget)
                    InjStart->ForceInjCurrRef = InjStart->ForceInjCurrTarget;

                MStruc->CRef2R.cd = InjStart->ForceInjCurrRef; //d�������ֵ
                MStruc->CRef2R.cq = 0;                         //q�������0

                //ע��Ƕȼ���
                TrigonoMetric_Function1(&(MStruc->Cossin),InjStart->ForceInjCurrAngle);
            }
            else//��λ��ɺ��ʼ����λ��صı���
            {
                if( _IQabs(MStruc->C2R_L.cd)>=_IQMPY_MACRO(InjStart->CurrErrThresh,\
                     _IQabs(MStruc->CRef2R.cd)))
                {
                    if( InjStart->ForceInjCounter >= InjStart->ForceInjStage)
                    {
                        InjStart->RampInjCurrRef = InjStart->ForceInjCurrRef; //�����ջ�����������ʼ��
                        InjStart->RampInjAngle = InjStart->ForceInjCurrAngle; //�����ջ������Ƕȳ�ʼ��
                        InjStart->Status = RAMP;                              //״̬���л�
                    }
                }
                else
                   Protection_SetFault(STARTUP_FAILURE_M); //����������ʼ�սϴ��ж�Ϊʧ��
           }
            break;

        case RAMP:
            if(InjStart->RampInjCounter < U32_MAX)
                InjStart->RampInjCounter++;//�����ջ�������ʱ����

            //�����ջ�����������ת�ٵ�б�´���
            if(InjStart->RampInjCurrRef < InjStart->RampInjCurrTarget)
            {
                InjStart->RampInjCurrRef += InjStart->RampInjCurrStep;
               if(InjStart->RampInjCurrRef >InjStart->RampInjCurrTarget)
                   InjStart->RampInjCurrRef =  InjStart->RampInjCurrTarget;
            }
            else if (InjStart->RampInjCurrRef > InjStart->RampInjCurrTarget)
            {
                InjStart->RampInjCurrRef -= InjStart->RampInjCurrStep;
               if(InjStart->RampInjCurrRef < InjStart->RampInjCurrTarget)
                   InjStart->RampInjCurrRef =  InjStart->RampInjCurrTarget;
            }

            if(InjStart->RampInjSpeedRef < InjStart->RampInjSpeedTarget)
            {
                InjStart->RampInjSpeedRef += InjStart->RampInjSpeedStep;
              if(InjStart->RampInjSpeedRef >InjStart->RampInjSpeedTarget)
                  InjStart->RampInjSpeedRef =  InjStart->RampInjSpeedTarget;
            }
            else if(InjStart->RampInjSpeedRef > InjStart->RampInjSpeedTarget)
            {
                InjStart->RampInjSpeedRef -= InjStart->RampInjSpeedStep;
              if(InjStart->RampInjSpeedRef < InjStart->RampInjSpeedTarget)
                  InjStart->RampInjSpeedRef =  InjStart->RampInjSpeedTarget;
            }

            //�����ջ������Ƕȼ���
            #if defined IF_VIBRATION_COM
            IF_Vibration_Com(&(MStruc->VRef2R), &(MStruc->C2R_L), InjStart);
            InjStart->RampInjAngle = InjStart->RampInjAngle +\
                                     _IQMPY_MACRO(InjStart->RampInjSpeedRef2,InjStart->K_WetoPosi);
            #else
            InjStart->RampInjAngle = InjStart->RampInjAngle +\
                                     _IQMPY_MACRO(InjStart->RampInjSpeedRef,InjStart->K_WetoPosi);
            #endif

            //�Ƕ�������0-1PU֮��
//            while(InjStart->RampInjAngle >= UNIT_Q24)
//                InjStart->RampInjAngle = InjStart->RampInjAngle - UNIT_Q24;
//            while(InjStart->RampInjAngle < 0)
//                InjStart->RampInjAngle = InjStart->RampInjAngle + UNIT_Q24;

            InjStart->RampInjAngle=( (InjStart->RampInjAngle) >= (0) ? \
                                         (InjStart->RampInjAngle&0XFFFFFF):\
                                         ((0XFFFFFF-(~InjStart->RampInjAngle))&0XFFFFFF) );

            //��ȡ�Ƕȵ����Һ�����ֵ
            TrigonoMetric_Function1(&(MStruc->Cossin),InjStart->RampInjAngle);
            MStruc->CRef2R.cd = InjStart->RampInjCurrRef;//d�������ֵ
            MStruc->CRef2R.cq = 0;//q�������0

            if(InjStart->RampInjCounter >= (InjStart->RampInjStage1))//LBG�۲��������ʼ��
            {
                if(_IQabs(MStruc->C2R_L.cd)>=_IQMPY_MACRO(InjStart->CurrErrThresh,\
                       _IQabs(MStruc->CRef2R.cd)))
                {
                    if(MLBG->Cal_Struc.Inimark == TRUE)
                        Injstart_Transition_Cal(InjStart,MStruc,MLBG);
                    else
                    {
                        LBG_Cal_Init(InjStart,MStruc,MLBG,&(LBG_SpeedBuffer_M[0]));
                        MLBG->Cal_Struc.Inimark = TRUE;
                    }
                }
                else //����������ʼ�սϴ��ж�Ϊʧ�� //��������
                        Protection_SetFault(STARTUP_FAILURE_M);
            }
            else
            {
                MLBG->Cal_Struc.CalphaPrev = MStruc->C2S.Alpha;//��ʼ��֮ǰ��ֵ���ӿ�����
                MLBG->Cal_Struc.CbetaPrev = MStruc->C2S.Beta;
            }
//            //һ��ʱ���ڻ�û��ת��ջ���������ʧ��
            if (InjStart->RampInjCounter >=InjStart->RampInjStage2)
                    Protection_SetFault(STARTUP_FAILURE_M);
           break;

        default:
            break;
    }
}
